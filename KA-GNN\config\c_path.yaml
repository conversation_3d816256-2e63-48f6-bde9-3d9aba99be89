model_select: "ka_gnn"
#ka_gnn, mlp_sage, kan_sage, kan_sage_two
select_dataset: "bace"
#bbbp, bace, clintox,sider,tox21,hiv,muv
force_field: 'uff'

encoder_atom: "cgcnn"
#encoder_method = ("atomic_number":1, "basic":11, "cfid":438, "cgcnn":92,'self':62,'gme':140)
encoder_bond: "dim_14"

pooling: 'avg'
#max,sum,avg
loss_sclect: 'bce'
grid_feat: 1
num_layers: 4
#l1,l2,sml1,bce
LR: 0.0001
NUM_EPOCHS: 501
batch_size: 128
train_ratio: 0.8
vali_ratio: 0.1
test_ratio: 0.1
iter: 1
